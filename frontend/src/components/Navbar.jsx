import { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { Menu, X, Zap, User, LogOut } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { useAuth } from '../contexts/AuthContext'
import UsageIndicator from './UsageIndicator'
import Logo from './Logo'

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false)
  const navigate = useNavigate()
  const { user, logout, isAuthenticated } = useAuth()

  const handleLogout = async () => {
    await logout()
    navigate('/')
    setIsOpen(false)
  }

  return (
    <nav className="backdrop-blur-md bg-slate-950/80 border-b border-blue-500/20 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* NeuroColony Branding */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center group">
              <Logo className="h-10 w-auto" variant="full" />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link
              to="/pricing"
              className="text-slate-300 hover:text-blue-400 font-medium transition-colors"
            >
              Pricing
            </Link>
            <Link
              to="/templates"
              className="text-slate-300 hover:text-blue-400 font-medium transition-colors"
            >
              Templates
            </Link>
            <Link
              to="/contact"
              className="text-slate-300 hover:text-blue-400 font-medium transition-colors"
            >
              Contact
            </Link>
            
            {isAuthenticated && user ? (
              <div className="flex items-center space-x-4">
                <UsageIndicator user={user} compact={true} />
                <Link 
                  to="/dashboard-working" 
                  className="text-neutral-300 hover:text-purple-400 font-medium transition-colors"
                >
                  Dashboard
                </Link>
                <Link 
                  to="/command-center" 
                  className="text-blue-300 hover:text-blue-400 font-bold transition-colors flex items-center gap-2 px-3 py-1 bg-blue-500/10 rounded-lg border border-blue-500/30"
                >
Command Center
                </Link>
                <Link 
                  to="/agent-dashboard" 
                  className="text-neutral-300 hover:text-purple-400 font-medium transition-colors flex items-center"
                >
Agents
                </Link>
                <Link 
                  to="/generator-simple" 
                  className="btn btn-primary"
                >
                  <Zap className="w-4 h-4" />
                  Generate
                </Link>
                
                {/* User Menu */}
                <div className="relative group">
                  <button className="flex items-center space-x-2 text-neutral-300 hover:text-white transition-colors">
                    <User className="w-5 h-5" />
                    <span className="hidden lg:block">{user.name}</span>
                  </button>
                  
                  {/* Dropdown */}
                  <div className="absolute right-0 mt-2 w-48 glass rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                    <div className="py-2">
                      <Link 
                        to="/dashboard-working" 
                        className="block px-4 py-2 text-sm text-neutral-300 hover:text-white hover:bg-purple-500/20 transition-colors"
                      >
                        Dashboard
                      </Link>
                      <Link 
                        to="/agent-dashboard" 
                        className="block px-4 py-2 text-sm text-neutral-300 hover:text-white hover:bg-purple-500/20 transition-colors"
                      >
AI Agents
                      </Link>
                      <Link 
                        to="/agent-marketplace" 
                        className="block px-4 py-2 text-sm text-neutral-300 hover:text-white hover:bg-purple-500/20 transition-colors"
                      >
Agent Marketplace
                      </Link>
                      <Link 
                        to="/agent-builder" 
                        className="block px-4 py-2 text-sm text-neutral-300 hover:text-white hover:bg-purple-500/20 transition-colors"
                      >
Agent Builder
                      </Link>
                      <Link 
                        to="/billing" 
                        className="block px-4 py-2 text-sm text-neutral-300 hover:text-white hover:bg-purple-500/20 transition-colors"
                      >
                        Billing
                      </Link>
                      <Link 
                        to="/settings" 
                        className="block px-4 py-2 text-sm text-neutral-300 hover:text-white hover:bg-purple-500/20 transition-colors"
                      >
                        Settings
                      </Link>
                      <button 
                        onClick={handleLogout}
                        className="w-full text-left px-4 py-2 text-sm text-red-400 hover:text-red-300 hover:bg-red-500/20 transition-colors"
                      >
                        <LogOut className="w-4 h-4 inline mr-2" />
                        Sign Out
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Link 
                  to="/login" 
                  className="text-neutral-300 hover:text-purple-400 font-medium transition-colors"
                >
                  Sign In
                </Link>
                <Link 
                  to="/register" 
                  className="btn btn-primary"
                >
                  Get Started
                </Link>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-neutral-300 hover:text-white p-2 rounded-lg transition-colors"
            >
              {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="md:hidden bg-neutral-900/95 backdrop-blur-sm border-t border-purple-500/20"
          >
            <div className="px-4 py-4 space-y-2">
              <Link 
                to="/pricing" 
                className="block py-2 text-neutral-300 hover:text-purple-400 transition-colors"
                onClick={() => setIsOpen(false)}
              >
                Pricing
              </Link>
              <Link 
                to="/contact" 
                className="block py-2 text-neutral-300 hover:text-purple-400 transition-colors"
                onClick={() => setIsOpen(false)}
              >
                Contact
              </Link>
              
              {isAuthenticated && user ? (
                <>
                  <div className="border-t border-neutral-700 pt-2 mt-2">
                    <div className="py-2">
                      <UsageIndicator user={user} compact={false} />
                    </div>
                  </div>
                  <Link 
                    to="/dashboard-working" 
                    className="block py-2 text-neutral-300 hover:text-purple-400 transition-colors"
                    onClick={() => setIsOpen(false)}
                  >
                    Dashboard
                  </Link>
                  <Link 
                    to="/generator-simple" 
                    className="block py-2 text-purple-400 font-medium"
                    onClick={() => setIsOpen(false)}
                  >
                    <Zap className="w-4 h-4 inline mr-2" />
                    Generate Sequence
                  </Link>
                  <button 
                    onClick={handleLogout}
                    className="block w-full text-left py-2 text-red-400 hover:text-red-300 transition-colors"
                  >
                    <LogOut className="w-4 h-4 inline mr-2" />
                    Sign Out
                  </button>
                </>
              ) : (
                <div className="border-t border-neutral-700 pt-2 mt-2 space-y-2">
                  <Link 
                    to="/login" 
                    className="block py-2 text-neutral-300 hover:text-purple-400 transition-colors"
                    onClick={() => setIsOpen(false)}
                  >
                    Sign In
                  </Link>
                  <Link 
                    to="/register" 
                    className="block py-2 text-purple-400 font-medium"
                    onClick={() => setIsOpen(false)}
                  >
                    Get Started
                  </Link>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  )
}

export default Navbar